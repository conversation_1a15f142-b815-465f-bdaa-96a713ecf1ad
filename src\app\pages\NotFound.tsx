import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ghost, Home } from "lucide-react";
import Link from "next/link";

const NotFound = () => {
  return (
    <div className="min-h-screen bg-background text-foreground flex items-center justify-center p-4">
      <Card className="bg-horror-midnight/50 border-horror-red/30 max-w-md w-full text-center">
        <CardContent className="p-8">
          <div className="mb-6">
            <Ghost className="h-24 w-24 text-horror-flame mx-auto mb-4 animate-pulse-horror" />
            <h1 className="text-6xl font-black text-horror-red mb-2">404</h1>
            <h2 className="text-2xl font-bold text-foreground mb-4">Page Not Found</h2>
            <p className="text-muted-foreground mb-6">
              Looks like this page got spooked away! The spirits have hidden it somewhere in the digital realm.
            </p>
          </div>
          <Button variant="horror" asChild className="w-full">
            <Link href="/" className="flex items-center justify-center space-x-2">
              <Home className="h-4 w-4" />
              <span>Return to Monster Jam</span>
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotFound;