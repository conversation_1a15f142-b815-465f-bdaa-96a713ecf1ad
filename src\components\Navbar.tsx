"use client";

import { useState } from "react";
import { Menu, X, Calendar, MapPin, Users, Ticket, Ghost, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);

  const navItems = [
    { name: "Event", href: "#event", icon: Calendar },
    { name: "Artists", href: "#artists", icon: Users },
    { name: "Venue", href: "#venue", icon: MapPin },
    { name: "Tickets", href: "#tickets", icon: Ticket },
  ];

  return (
    <nav className="fixed top-0 w-full z-50 bg-horror-midnight/95 backdrop-blur-md overflow-hidden">
      {/* Spooky floating elements */}
      <div className="absolute inset-0 pointer-events-none">
        <Ghost className="absolute top-2 left-10 h-4 w-4 text-horror-red/30 animate-float opacity-60" />
        <Skull className="absolute top-3 right-20 h-3 w-3 text-horror-flame/40 animate-pulse-horror opacity-50" />
        <div className="absolute top-4 left-1/3 w-1 h-1 bg-horror-ember rounded-full animate-flicker opacity-70" />
        <div className="absolute top-2 right-1/4 w-2 h-2 bg-horror-red rounded-full animate-float opacity-40" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="monster-title text-2xl font-bold">
              MONSTER JAM
            </div>
            <span className="text-horror-flame text-sm font-medium animate-pulse-horror">BKK</span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="flex items-center space-x-1 text-foreground hover:text-horror-flame transition-colors duration-300 group"
              >
                <item.icon className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                <span>{item.name}</span>
              </a>
            ))}
            <Button
              variant="horror"
              size="sm"
              onClick={() => window.open('https://www.ticketmelon.com/th/Monsterjam-bkk/HalloweenParty/', '_blank')}
            >
              Get Tickets
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghostHorror"
              size="icon"
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden pb-4 animate-fade-in-up">
            <div className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-2 text-foreground hover:text-horror-flame transition-colors duration-300 py-2"
                  onClick={() => setIsOpen(false)}
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </a>
              ))}
              <Button
                variant="horror"
                className="w-full mt-4"
                onClick={() => window.open('https://www.ticketmelon.com/th/Monsterjam-bkk/HalloweenParty/', '_blank')}
              >
                Get Tickets
              </Button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;