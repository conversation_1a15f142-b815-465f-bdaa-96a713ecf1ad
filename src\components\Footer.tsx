import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, Clock, Phone, Mail, Instagram, Ghost, Skull } from "lucide-react";

const Footer = () => {

  const socialLinks = [
    { icon: Instagram, href: "https://www.instagram.com/monsterjam.bkk/", name: "Instagram" },
  ];

  return (
    <footer className="bg-horror-shadow border-t border-horror-red/20 relative overflow-hidden">
      {/* Spooky footer effects */}
      <div className="absolute inset-0 pointer-events-none">
        <Ghost className="absolute top-8 left-6 h-6 w-6 text-horror-red/15 animate-float opacity-25" />
        <Skull className="absolute top-12 right-8 h-5 w-5 text-horror-flame/20 animate-pulse-horror opacity-30" />
        <Ghost className="absolute bottom-8 left-1/4 h-4 w-4 text-horror-ember/18 animate-float opacity-20" />
        <div className="absolute top-6 right-1/3 w-2 h-2 bg-horror-red rounded-full animate-flicker opacity-25" />
        <div className="absolute bottom-6 right-12 w-1 h-1 bg-horror-flame rounded-full animate-pulse-horror opacity-30" />
      </div>

      <div className="container mx-auto px-4 py-16 relative z-10">
        {/* Main Footer Content */}
        <div className="grid lg:grid-cols-4 gap-8 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="monster-title text-4xl font-black mb-4 horror-title-text">
              MONSTER JAM
            </div>
            <p className="text-muted-foreground mb-6 max-w-md font-body-readable">
              The biggest Halloween party in Bangkok featuring top DJs,
              celebrity guests, and spine-chilling entertainment you'll never forget.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <Button
                  key={social.name}
                  variant="ghostHorror"
                  size="icon"
                  asChild
                >
                  <a href={social.href} aria-label={social.name}>
                    <social.icon className="h-5 w-5" />
                  </a>
                </Button>
              ))}
            </div>
          </div>

          {/* Event Info */}
          <div>
            <h4 className="text-lg font-bold text-horror-flame mb-4">Event Information</h4>
            <div className="space-y-3 text-sm text-muted-foreground">
              <div className="flex items-start space-x-2">
                <Clock className="h-4 w-4 text-horror-flame mt-0.5 flex-shrink-0" />
                <div>
                  <div className="font-semibold text-foreground">October 30, 2024</div>
                  <div>16:00</div>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 text-horror-flame mt-0.5 flex-shrink-0" />
                <div>
                  <div className="font-semibold text-foreground">JJ Mall Bangkok</div>
                  <div>6th Floor, Chatuchak District</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-horror-red font-semibold">16+ Only</span>
                <span>•</span>
                <span>ID Required</span>
              </div>
            </div>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-lg font-bold text-horror-flame mb-4">Contact Us</h4>
            <div className="space-y-3 text-sm text-muted-foreground">
              {/* <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-horror-flame" />
                <span>+66 (0) 2-XXX-XXXX</span> */}
              {/* </div> */}
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-horror-flame" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>


        {/* Bottom Bar */}
        <div className="border-t border-horror-red/20 pt-8">
          <div className="text-center text-sm text-muted-foreground">
            <div>
              <span className="font-semibold text-foreground">© 2024 MonsterJam BKK.</span>
              <span className="ml-1">All rights reserved.</span>
            </div>
          </div>
          
          <div className="mt-4 text-center text-xs text-muted-foreground">
            <p>
              Event organized in partnership with JJ Mall Shopping Center. 
              Please drink responsibly. 20+ for alcohol consumption.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;