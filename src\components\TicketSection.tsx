"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Star, Crown, Gift, Ghost, Skull, Zap, Eye } from "lucide-react";

const TicketSection = () => {
  const ticketTiers = [
    {
      name: "GA Early Bird",
      price: "1,800",
      originalPrice: "2,500",
      status: "Limited Time",
      features: ["General Admission", "Access to all zones", "Food court access"],
      color: "horror-flame",
      icon: Gift,
      badge: "SAVE 700 THB"
    },
    {
      name: "VIP Early Bird", 
      price: "3,000",
      originalPrice: "3,500",
      status: "Limited Time",
      features: ["VIP Zone Access", "Premium bar", "Fast track entry"],
      color: "horror-ember",
      icon: Star,
      badge: "SAVE 500 THB"
    },
    {
      name: "GA Tier 1",
      price: "2,500",
      originalPrice: null,
      status: "Available",
      features: ["General Admission", "Access to all zones", "Food court access"],
      color: "horror-red",
      icon: CheckCircle,
      badge: null
    },
    {
      name: "VIP Tier 1",
      price: "3,500", 
      originalPrice: null,
      status: "Available",
      features: ["VIP Zone Access", "Premium bar", "Fast track entry"],
      color: "horror-blood",
      icon: Crown,
      badge: null
    }
  ];

  const vipPackages = [
    {
      name: "VIP Supreme",
      price: "100,000",
      pax: "8 PAX",
      color: "bg-blue-500",
      includes: ["1 Azul", "1 XO Hennessy", "1 Don Julio 1942", "2 Moët & Chandon", "10 Mixers", "2 Ice Buckets"],
      perks: ["Personal Butler", "Private Bar", "VIP Entrance", "Special Merchandise"]
    },
    {
      name: "VIP Premium",
      price: "60,000",
      pax: "6 PAX", 
      color: "bg-yellow-500",
      includes: ["1 Azul", "1 Hennessy V.S.O.P", "2 Moët & Chandon", "10 Mixers", "2 Ice Buckets"],
      perks: ["Personal Butler", "Private Bar", "VIP Entrance", "Merchandise"]
    },
    {
      name: "VIP Table",
      price: "35,000",
      pax: "4 PAX",
      color: "bg-green-500", 
      includes: ["1 Hennessy V.S.O.P", "1 Grey Goose Vodka", "6 Beer Cans", "6 Mixers", "1 Ice Bucket"],
      perks: ["Personal Butler", "Private Bar", "VIP Entrance"]
    },
    {
      name: "VIP Stand",
      price: "25,000",
      pax: "4 PAX",
      color: "bg-gray-300",
      includes: ["1 Hennessy VS OR 1 Grey Goose Vodka", "4 Beer Cans", "4 Mixers", "1 Ice Bucket"],
      perks: ["VIP Entrance"]
    }
  ];

  return (
    <section id="tickets" className="py-12 bg-gradient-to-b from-background to-horror-midnight relative overflow-hidden">
      {/* Spooky floating elements */}
      <div className="absolute inset-0 pointer-events-none">
        <Ghost className="absolute top-10 left-5 h-6 w-6 text-horror-red/20 animate-float opacity-40" />
        <Skull className="absolute top-20 right-10 h-5 w-5 text-horror-flame/30 animate-pulse-horror opacity-50" />
        <Eye className="absolute top-32 left-1/4 h-4 w-4 text-horror-ember/25 animate-flicker opacity-60" />
        <Ghost className="absolute bottom-20 right-1/3 h-7 w-7 text-horror-red/15 animate-float opacity-30" />
        <Zap className="absolute bottom-40 left-1/5 h-3 w-3 text-horror-flame/40 animate-pulse-horror opacity-50" />
        <div className="absolute top-16 right-1/4 w-2 h-2 bg-horror-red rounded-full animate-flicker opacity-30" />
        <div className="absolute bottom-32 left-10 w-1 h-1 bg-horror-ember rounded-full animate-float opacity-50" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-8 animate-fade-in-up">
          <h2 className="text-4xl md:text-6xl font-black mb-4">
            <span className="monster-title spooky-text">GET YOUR TICKETS</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-body-readable">
            Don't miss out on Bangkok's biggest Halloween party! Limited tickets available.
          </p>
        </div>

        {/* Ticket Images */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8 animate-fade-in-up">
          <Card className="bg-horror-midnight/50 border-horror-red/30 horror-glow">
            <CardContent className="p-0">
              <img 
                src="/images/ticket.jpg" 
                alt="Ticket Information" 
                className="w-full h-auto rounded-lg"
              />
            </CardContent>
          </Card>
          <Card className="bg-horror-midnight/50 border-horror-red/30 horror-glow">
            <CardContent className="p-0">
              <img 
                src="/images/viptype.jpg" 
                alt="VIP Package Details" 
                className="w-full h-auto rounded-lg"
              />
            </CardContent>
          </Card>
        </div>

        {/* Regular Ticket Tiers */}
        <div className="mb-8 animate-fade-in-up">
          <h3 className="text-2xl md:text-3xl font-bold text-center mb-6">
            <span className="text-horror-flame">Individual Tickets</span>
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {ticketTiers.map((ticket, index) => (
              <Card 
                key={ticket.name}
                className={`bg-horror-midnight/40 border-${ticket.color}/30 hover:border-${ticket.color}/60 transition-all duration-300 hover:scale-105 relative overflow-hidden group`}
              >
                {ticket.badge && (
                  <div className="absolute top-4 right-4 z-10">
                    <Badge variant="destructive" className="bg-horror-red font-bold">
                      {ticket.badge}
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-2">
                    <ticket.icon className={`h-6 w-6 text-${ticket.color} group-hover:animate-pulse-horror`} />
                    <span className={`text-sm font-semibold text-${ticket.color} uppercase tracking-wide`}>
                      {ticket.status}
                    </span>
                  </div>
                  <CardTitle className="text-xl text-foreground group-hover:text-horror-flame transition-colors duration-300 font-body-readable">
                    {ticket.name}
                  </CardTitle>
                </CardHeader>
                
                <CardContent>
                  <div className="mb-4">
                    <div className="flex items-baseline space-x-2">
                      <span className="text-3xl font-black text-horror-flame">
                        ฿{ticket.price}
                      </span>
                      {ticket.originalPrice && (
                        <span className="text-lg text-muted-foreground line-through font-body-readable">
                          ฿{ticket.originalPrice}
                        </span>
                      )}
                    </div>
                    <span className="text-sm text-muted-foreground font-body-readable">THB per person</span>
                  </div>
                  
                  <ul className="space-y-2 mb-6">
                    {ticket.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-muted-foreground font-body-readable">
                        <CheckCircle className="h-4 w-4 text-horror-flame mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button
                    variant="horror"
                    className="w-full"
                    onClick={() => window.open('https://www.ticketmelon.com/th/Monsterjam-bkk/HalloweenParty/', '_blank')}
                  >
                    Buy Now
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* VIP Packages */}
        <div className="mb-8 animate-fade-in-up">
          <h3 className="text-2xl md:text-3xl font-bold text-center mb-6">
            <span className="text-horror-ember font-body-readable">Premium VIP Packages</span>
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {vipPackages.map((pkg, index) => (
              <Card
                key={pkg.name}
                className="bg-horror-midnight/40 border-horror-ember/30 hover:border-horror-flame/60 transition-all duration-300 hover:scale-105 group flex flex-col h-full"
              >
                <CardHeader>
                  <div className={`w-full h-16 ${pkg.color} rounded-lg mb-4 flex items-center justify-center`}>
                    <span className="text-black font-bold text-lg font-body-readable">{pkg.name}</span>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-black text-horror-flame font-body-readable">
                      ฿{pkg.price}
                    </div>
                    <div className="text-sm text-muted-foreground font-semibold font-body-readable">
                      {pkg.pax} (Additional THB 3,000)
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="flex-grow flex flex-col">
                  <div className="flex-grow">
                    <div className="mb-4">
                      <h5 className="text-sm font-bold text-horror-ember mb-2 font-body-readable">Includes:</h5>
                      <ul className="space-y-1">
                        {pkg.includes.map((item, idx) => (
                          <li key={idx} className="text-xs text-muted-foreground flex items-start font-body-readable">
                            <span className="text-horror-flame mr-1">•</span>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="mb-4">
                      <h5 className="text-sm font-bold text-horror-ember mb-2 font-body-readable">Perks:</h5>
                      <div className="flex flex-wrap gap-1">
                        {pkg.perks.map((perk, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs border-horror-flame/30">
                            {perk}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="premium"
                    className="w-full mt-auto"
                    onClick={() => window.open('https://www.ticketmelon.com/th/Monsterjam-bkk/HalloweenParty/', '_blank')}
                  >
                    Reserve Now
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Ticket Notes */}
        <div className="mt-8 text-center animate-fade-in-up">
          <Card className="bg-horror-midnight/30 border-horror-red/20 max-w-4xl mx-auto relative overflow-hidden">
            {/* Ghost effects around the card */}
            <div className="absolute inset-0 pointer-events-none">
              <Ghost className="absolute top-2 right-4 h-4 w-4 text-horror-red/20 animate-float opacity-30" />
              <Skull className="absolute bottom-3 left-6 h-3 w-3 text-horror-flame/25 animate-pulse-horror opacity-40" />
              <Eye className="absolute top-4 left-1/3 h-3 w-3 text-horror-ember/20 animate-flicker opacity-35" />
            </div>
            <CardContent className="p-4 md:p-8 relative z-10">
              <h4 className="text-xl font-bold text-horror-flame mb-4 font-body-readable">Important Information</h4>
              <div className="grid md:grid-cols-2 gap-6 text-sm text-muted-foreground font-body-readable">
                <div>
                  <h5 className="font-semibold text-foreground mb-2 font-body-readable">Ticket Policy</h5>
                  <ul className="space-y-1 text-left font-body-readable">
                    <li>• 16+ age restriction strictly enforced</li>
                    <li>• Valid ID required for entry</li>
                    <li>• No refunds or exchanges</li>
                    <li>• Limited tickets available</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-semibold text-foreground mb-2 font-body-readable">Event Details</h5>
                  <ul className="space-y-1 text-left font-body-readable">
                    <li>• Date: 30 ตุลาคม 2025</li>
                    <li>• Time: 16:00 - 04:00</li>
                    <li>• Venue: JJ Mall 6th Floor</li>
                    <li>• Dress code: Halloween costume encouraged</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default TicketSection;