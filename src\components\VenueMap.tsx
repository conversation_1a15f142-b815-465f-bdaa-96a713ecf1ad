import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { MapPin, Navigation, Users, Gamepad2, Skull, Ghost, Zap, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";

const VenueMap = () => {
  const zones = [
    {
      id: "A",
      name: "Red Tier Booths Zone",
      description: "Food & Beverage Booths",
      icon: Users,
      color: "horror-red"
    },
    {
      id: "B", 
      name: "Entrance & Registration",
      description: "Main Entry Point",
      icon: Navigation,
      color: "horror-flame"
    },
    {
      id: "C",
      name: "Game Zone & Haunted Maze",
      description: "Interactive Entertainment",
      icon: Gamepad2,
      color: "horror-ember"
    },
    {
      id: "D",
      name: "Concert Area & Main Stage",
      description: "Live Performances",
      icon: MapPin,
      color: "horror-blood"
    }
  ];

  const facilities = [
    { icon: "💀", name: "Restrooms", description: "Multiple haunted locations" },
    { icon: "👻", name: "Elevators", description: "Ghostly easy access" },
    { icon: "🎃", name: "Escalators", description: "Spooky quick movement" },
  ];

  return (
    <section id="venue" className="py-20 bg-gradient-to-b from-horror-midnight to-background relative overflow-hidden">
      {/* Halloween floating effects */}
      <div className="absolute inset-0 pointer-events-none">
        <Ghost className="absolute top-16 left-8 h-8 w-8 text-horror-red/15 animate-float opacity-30" />
        <Skull className="absolute top-32 right-12 h-6 w-6 text-horror-flame/20 animate-pulse-horror opacity-40" />
        <Zap className="absolute bottom-24 left-1/4 h-5 w-5 text-horror-ember/25 animate-flicker opacity-35" />
        <Ghost className="absolute bottom-40 right-1/5 h-7 w-7 text-horror-red/18 animate-float opacity-25" />
        <div className="absolute top-24 left-1/3 w-3 h-3 bg-horror-flame rounded-full animate-pulse-horror opacity-20" />
        <div className="absolute bottom-32 right-1/3 w-2 h-2 bg-horror-ember rounded-full animate-flicker opacity-30" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl md:text-6xl font-black mb-4">
            <span className="monster-title spooky-text">VENUE MAP</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            JJ Mall 6th Floor, Chatuchak District, Bangkok - Navigate through four epic zones of Halloween mayhem
          </p>
        </div>

        {/* Venue Address Card */}
        <div className="mb-12 animate-fade-in-up">
          <Card className="bg-horror-midnight/50 border-horror-red/30 max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center text-horror-flame flex items-center justify-center">
                <MapPin className="h-5 w-5 mr-2" />
                Event Location
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-lg font-semibold text-foreground">JJ Mall Shopping Center</p>
              <p className="text-muted-foreground">6th Floor</p>
              <p className="text-muted-foreground mb-4">Chatuchak Subdistrict, Chatuchak, Bangkok</p>
              <Button
                variant="horror"
                size="sm"
                className="mx-auto"
                onClick={() => window.open('https://www.google.com/maps?sca_esv=37b289db0e7768a1&output=search&q=jj+mall&source=lnms&fbs=AIIjpHyWiSnpQCGUGrY4hqD0-IhT0udjQwF5vdHCNMe0U7lt6x206mXmhS27ErIQlhIBxZgT2n7bjWbhn9bkiUl02I29Wxt-jypgjE3OO4vbDD7s6KrI-Qq8D2vN2ln39UbzxhJoCrpH8gy8KmEd6qaJwE-d4DFfqtCxamKbcPFC2CW795eVQiM0c1eLZ1r3Mrx0qiNZ50lkKedHNYDusM-tLlWeWWdtfM7f-xAK1x5C9vKVmtseeNvL89iZucTKuQLZRVwj_6xDLrc1LO-ZI4F94Fua3eCf27ut7eZOz-EjCsSqOg5VWj8&entry=mc&ved=1t:200715&ictx=111', '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Navigate with Google Maps
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Interactive Venue Map */}
        <div className="mb-16 animate-fade-in-up">
          <Card className="bg-horror-shadow/50 border-horror-red/30 horror-glow">
            <CardContent className="p-0">
              <img 
                src="/images/zoneposter.jpg" 
                alt="MonsterJam BKK Venue Map" 
                className="w-full h-auto rounded-lg"
              />
            </CardContent>
          </Card>
        </div>

        {/* Zone Directory */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 animate-fade-in-up">
          {zones.map((zone) => (
            <Card 
              key={zone.id}
              className="bg-horror-midnight/40 border-horror-red/20 hover:border-horror-flame/50 transition-all duration-300 hover:scale-105 cursor-pointer group"
            >
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className={`text-2xl font-black text-${zone.color} group-hover:animate-pulse-horror`}>
                    ZONE {zone.id}
                  </div>
                  <zone.icon className={`h-6 w-6 text-${zone.color} group-hover:scale-110 transition-transform duration-300`} />
                </div>
              </CardHeader>
              <CardContent>
                <h4 className="font-bold text-foreground mb-1 group-hover:text-horror-flame transition-colors duration-300">
                  {zone.name}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {zone.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Facilities & Services */}
        <div className="animate-fade-in-up">
          <Card className="bg-horror-midnight/30 border-horror-red/20">
            <CardHeader>
              <CardTitle className="text-center text-horror-flame">
                Facilities & Services
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                {facilities.map((facility, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl mb-2">{facility.icon}</div>
                    <h5 className="font-semibold text-foreground mb-1">{facility.name}</h5>
                    <p className="text-sm text-muted-foreground">{facility.description}</p>
                  </div>
                ))}
              </div>
              
              <div className="mt-8 text-center">
                <p className="text-sm text-muted-foreground">
                  <strong className="text-horror-flame">Opening Hours:</strong> Event Day 16:00
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  <strong className="text-horror-ember">Age Restriction:</strong> 16+ (ID Required)
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default VenueMap;