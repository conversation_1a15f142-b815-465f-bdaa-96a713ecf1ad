import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Eye } from "lucide-react";

const ArtistLineup = () => {
  const featuredArtists = [
    "OG BOBBY", "4BANG", "1MILL", "<PERSON>HA<PERSON>", "<PERSON><PERSON><PERSON>Y<PERSON>A<PERSON>", "TWOPEE",
    "FIIXD", "GUNNE<PERSON>", "MI<PERSON>IEMIXX", "DJ NAT", "DJ BUDDAFLY", "DJ JONG",
    "DJ TOB", "DJ DOUBLE E", "PCHAI NIGEL", "DJ JETSKEE"
  ];

  const specialGuests = [
    "SUNNYBONE UDT", "TPHUNNID", "SRIP<PERSON><PERSON>", "THAI<PERSON><PERSON><PERSON>", 
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NAMEMT"
  ];

  return (
    <section id="artists" className="py-20 bg-gradient-to-b from-background to-horror-midnight relative overflow-hidden">
      {/* Spooky floating elements */}
      <div className="absolute inset-0 pointer-events-none">
        <Ghost className="absolute top-12 right-6 h-7 w-7 text-horror-red/20 animate-float opacity-35" />
        <Skull className="absolute top-28 left-8 h-5 w-5 text-horror-flame/25 animate-pulse-horror opacity-40" />
        <Eye className="absolute bottom-20 right-1/4 h-4 w-4 text-horror-ember/30 animate-flicker opacity-45" />
        <Zap className="absolute bottom-36 left-1/5 h-6 w-6 text-horror-red/18 animate-pulse-horror opacity-30" />
        <Ghost className="absolute top-40 left-1/3 h-5 w-5 text-horror-flame/15 animate-float opacity-25" />
        <div className="absolute top-20 right-1/3 w-2 h-2 bg-horror-ember rounded-full animate-flicker opacity-35" />
        <div className="absolute bottom-28 left-12 w-3 h-3 bg-horror-red rounded-full animate-pulse-horror opacity-25" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl md:text-6xl font-black mb-4">
            <span className="monster-title spooky-text">ARTIST LINEUP</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto font-body-readable">
            Thailand's hottest DJs and celebrity guest judges bringing the heat to MonsterJam BKK
          </p>
        </div>

        {/* Main Artist Poster */}
        <div className="mb-16 animate-fade-in-up">
          <Card className="bg-horror-midnight/50 border-horror-red/30 horror-glow">
            <CardContent className="p-0">
              <img 
                src="/images/DJposter.jpg" 
                alt="MonsterJam BKK DJ Lineup" 
                className="w-full h-auto rounded-lg"
              />
            </CardContent>
          </Card>
        </div>

        {/* Artist Categories */}
        <div className="grid md:grid-cols-2 gap-12 animate-fade-in-up">
          {/* Featured DJs */}
          <div>
            <h3 className="text-2xl font-bold text-horror-flame mb-6 flex items-center">
              <span className="w-4 h-4 bg-horror-flame rounded-full mr-3 animate-pulse-horror" />
              Featured DJs
            </h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
              {featuredArtists.map((artist, index) => (
                <Card 
                  key={artist}
                  className="bg-horror-shadow/70 border-horror-red/20 hover:border-horror-flame/50 transition-all duration-300 hover:scale-105 cursor-pointer group"
                >
                  <CardContent className="p-4 text-center">
                    <div className="text-sm font-bold text-foreground group-hover:text-horror-flame transition-colors duration-300 font-body-readable">
                      {artist}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Special Celebrity Guests */}
          <div>
            <h3 className="text-2xl font-bold text-horror-ember mb-6 flex items-center">
              <span className="w-4 h-4 bg-horror-ember rounded-full mr-3 animate-flicker" />
              Celebrity Guest Judges
            </h3>
            <div className="grid grid-cols-2 gap-4">
              {specialGuests.map((guest, index) => (
                <Card 
                  key={guest}
                  className="bg-horror-shadow/70 border-horror-ember/20 hover:border-horror-red/50 transition-all duration-300 hover:scale-105 cursor-pointer group"
                >
                  <CardContent className="p-4 text-center">
                    <div className="text-sm font-bold text-foreground group-hover:text-horror-ember transition-colors duration-300 font-body-readable">
                      {guest}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Event Features */}
        <div className="mt-16 text-center animate-fade-in-up">
          <div className="bg-horror-midnight/30 rounded-lg p-8 border border-horror-red/20">
            <h4 className="text-xl font-bold text-horror-flame mb-4">Plus Much More!</h4>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
              <span className="flex items-center">
                <span className="w-2 h-2 bg-horror-red rounded-full mr-2" />
                Local Food Vendors
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-horror-flame rounded-full mr-2" />
                Interactive Games
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-horror-ember rounded-full mr-2" />
                Face Painting
              </span>
              <span className="flex items-center">
                <span className="w-2 h-2 bg-horror-red rounded-full mr-2" />
                Photo Booths
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ArtistLineup;