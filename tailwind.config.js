/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        // MonsterJam Horror Theme
        horror: {
          red: "hsl(var(--horror-red))",
          blood: "hsl(var(--blood-red))",
          flame: "hsl(var(--flame-orange))",
          ember: "hsl(var(--ember-orange))",
          shadow: "hsl(var(--shadow-black))",
          midnight: "hsl(var(--midnight-black))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        'creepster': ['var(--font-creepster)', 'cursive'],
        'inter': ['var(--font-inter)', 'system-ui', 'sans-serif'],
        'changa-one': ['var(--font-changa-one)', 'sans-serif'],
        'spooky': ['Creepster', 'Nosifer', 'Metal Mania', 'fantasy'],
        'horror-title': ['Butcherman', 'Creepster', 'cursive'],
        'horror-display': ['Eater', 'Creepster', 'fantasy'],
        'spooky-fun': ['Jolly Lodger', 'Griffy', 'cursive'],
        'readable-spooky': ['var(--font-changa-one)', 'Fredoka One', 'sans-serif'],
        'body-readable': ['var(--font-inter)', 'system-ui', 'sans-serif'],
        'metal': ['Metal Mania', 'cursive'],
        'griffy': ['Griffy', 'cursive'],
        'butcherman': ['Butcherman', 'cursive'],
        'eater': ['Eater', 'fantasy'],
        'jolly': ['Jolly Lodger', 'cursive'],
        'changa': ['var(--font-changa-one)', 'sans-serif'],
        'fredoka': ['Fredoka One', 'sans-serif'],
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        // MonsterJam Horror Animations
        "flicker": "flicker 2s infinite alternate",
        "pulse-horror": "pulse-horror 3s ease-in-out infinite",
        "float": "float 6s ease-in-out infinite",
        "fade-in-up": "fade-in-up 0.6s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}

