import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>_One } from "next/font/google";
import "./globals.css";
import "./app.css";

const creepster = Creepster({
  variable: "--font-creepster",
  subsets: ["latin"],
  weight: "400",
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const changaOne = Changa_One({
  variable: "--font-changa-one",
  subsets: ["latin"],
  weight: "400",
});

export const metadata: Metadata = {
  title: "MONSTERJAM Bangkok - Halloween Party 2025",
  description: "Bangkok's biggest costume Halloween party on 30 ตุลาคม 2025 at JJ Mall Bangkok",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${creepster.variable} ${inter.variable} ${changaOne.variable} antialiased font-body-readable`}
      >
        {children}
      </body>
    </html>
  );
}
