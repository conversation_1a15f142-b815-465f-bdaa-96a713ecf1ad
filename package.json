{"name": "monsterjambkk.com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.89.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "next": "15.4.6", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "typescript": "^5"}}