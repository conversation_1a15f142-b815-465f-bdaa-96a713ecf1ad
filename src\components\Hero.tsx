"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, MapPin, Clock } from "lucide-react";
import monster<PERSON><PERSON><PERSON>ogo from "@/assets/monster-jam-logo.jpg";

const Hero = () => {
  return (
    <section id="event" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 gradient-shadow">
        <div 
          className="absolute inset-0 opacity-30 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${monsterJamLogo})` }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-horror-shadow via-horror-midnight/80 to-transparent" />
      </div>

      {/* Floating Halloween Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Animated Embers */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-horror-flame rounded-full animate-float opacity-70" />
        <div className="absolute top-40 right-20 w-1 h-1 bg-horror-ember rounded-full animate-pulse-horror opacity-60" />
        <div className="absolute bottom-32 left-1/4 w-3 h-3 bg-horror-red rounded-full animate-flicker opacity-50" />
        <div className="absolute bottom-20 right-1/3 w-2 h-2 bg-horror-flame rounded-full float-animation opacity-60" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
        {/* Event Badges */}
        <div className="flex flex-wrap justify-center gap-4 mb-8 animate-fade-in-up">
          <div className="bg-horror-red/20 border border-horror-red/40 px-4 py-2 rounded-lg backdrop-blur-sm">
            <div className="flex items-center space-x-2 text-horror-red">
              <Calendar className="h-4 w-4" />
              <span className="font-semibold font-body-readable">October 30, 2024</span>
            </div>
          </div>
          <div className="bg-horror-flame/20 border border-horror-flame/40 px-4 py-2 rounded-lg backdrop-blur-sm">
            <div className="flex items-center space-x-2 text-horror-flame">
              <Clock className="h-4 w-4" />
              <span className="font-semibold font-body-readable">16+ Only</span>
            </div>
          </div>
          <div className="bg-horror-ember/20 border border-horror-ember/40 px-4 py-2 rounded-lg backdrop-blur-sm">
            <div className="flex items-center space-x-2 text-horror-ember">
              <MapPin className="h-4 w-4" />
              <span className="font-semibold font-body-readable">JJ Mall Bangkok</span>
            </div>
          </div>
        </div>

        {/* Main Title */}
        <h1 className="text-6xl md:text-8xl lg:text-9xl font-black mb-4 animate-fade-in-up">
          <span className="block text-foreground mb-2">THE BIGGEST</span>
          <span className="block text-horror-flame mb-2">HALLOWEEN PARTY</span>
          <span className="block text-foreground mb-4">IN BANGKOK</span>
          <div className="monster-title text-[6rem] sm:text-[7rem] md:text-[8rem] lg:text-[9rem] font-black tracking-wider flicker-animation spooky-text">
            MONSTER JAM
          </div>
        </h1>

        {/* Subtitle */}
        <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-fade-in-up font-body-readable">
          Join Thailand&apos;s most epic Halloween celebration featuring top DJs,
          celebrity guests, spine-chilling entertainment, and unforgettable thrills
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up">
          <Button
            variant="horror"
            size="lg"
            className="text-lg px-8 py-4"
            onClick={() => window.open('https://www.ticketmelon.com/th/Monsterjam-bkk/HalloweenParty/', '_blank')}
          >
            Get Your Tickets Now
          </Button>
          <Button variant="ghostHorror" size="lg" className="text-lg px-8 py-4">
            View Event Details
          </Button>
        </div>

        {/* Event Stats */}
        <div className="grid grid-cols-3 gap-8 mt-16 animate-fade-in-up">
          <div className="text-center">
            <div className="text-3xl font-bold text-horror-flame flicker-animation font-readable-spooky">25+</div>
            <div className="text-sm text-muted-foreground uppercase tracking-wide font-body-readable">TOP DJs</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-horror-red flicker-animation font-readable-spooky">4</div>
            <div className="text-sm text-muted-foreground uppercase tracking-wide font-body-readable">Zones</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-horror-ember flicker-animation font-readable-spooky">VIP</div>
            <div className="text-sm text-muted-foreground uppercase tracking-wide font-body-readable">Packages</div>
          </div>
        </div>
      </div>

      {/* Bottom Gradient Fade */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent" />
    </section>
  );
};

export default Hero;