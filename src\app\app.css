/* Horror Theme Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Creepster:wght@400&family=Nosifer:wght@400&family=Metal+Mania:wght@400&family=Butcherman:wght@400&family=Eater:wght@400&family=Changa+One:wght@400;700&family=Fredoka+One:wght@400&family=Griffy:wght@400&family=Jolly+Lodger:wght@400&display=swap');

:root {
  /* Horror Color Palette */
  --horror-red: #ff0000;
  --horror-blood: #8b0000;
  --horror-flame: #ff4500;
  --horror-ember: #ff6347;
  --horror-midnight: #1a0000;
  --horror-shadow: #330000;

  /* Animation Timings */
  --horror-flicker-duration: 0.15s;
  --horror-pulse-duration: 2s;
  --horror-float-duration: 3s;
}

/* Enhanced Monster Title Font */
.monster-title {
  font-family: 'Butcherman', 'Metal Mania', cursive;
  text-shadow:
    0 0 20px rgba(255, 0, 0, 0.9),
    0 0 40px rgba(255, 69, 0, 0.7),
    0 0 60px rgba(255, 99, 71, 0.5),
    4px 4px 8px rgba(0, 0, 0, 0.9);
  letter-spacing: 0.15em;
  font-weight: 400;
}

/* Additional Horror Font Classes */
.horror-subtitle {
  font-family: 'Eater', 'Creepster', fantasy;
  text-shadow:
    0 0 15px rgba(255, 69, 0, 0.8),
    0 0 25px rgba(255, 99, 71, 0.6),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.1em;
}

.spooky-readable {
  font-family: 'Changa One', 'Fredoka One', sans-serif;
  text-shadow:
    0 0 10px rgba(255, 0, 0, 0.6),
    0 0 20px rgba(255, 69, 0, 0.4),
    1px 1px 2px rgba(0, 0, 0, 0.7);
  letter-spacing: 0.05em;
  font-weight: 400;
}

.fun-spooky {
  font-family: 'Jolly Lodger', 'Griffy', cursive;
  text-shadow:
    0 0 8px rgba(255, 99, 71, 0.7),
    0 0 16px rgba(255, 140, 0, 0.5),
    1px 1px 3px rgba(0, 0, 0, 0.7);
  letter-spacing: 0.08em;
}

.metal-text {
  font-family: 'Metal Mania', cursive;
  text-shadow:
    0 0 12px rgba(255, 0, 0, 0.8),
    0 0 24px rgba(139, 0, 0, 0.6),
    2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.12em;
}

.readable-body {
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

.readable-heading {
  font-family: 'Changa One', 'Inter', sans-serif;
  font-weight: 400;
  line-height: 1.3;
  letter-spacing: 0.03em;
}

/* Horror Animations */
@keyframes flicker-animation {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes pulse-horror {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes float-animation {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes ember-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--horror-flame);
  }
  50% {
    box-shadow: 0 0 20px var(--horror-flame), 0 0 30px var(--horror-red);
  }
}

/* Animation Classes */
.flicker-animation {
  animation: flicker-animation var(--horror-flicker-duration) infinite alternate;
}

.animate-pulse-horror {
  animation: pulse-horror var(--horror-pulse-duration) infinite;
}

.animate-float {
  animation: float-animation var(--horror-float-duration) ease-in-out infinite;
}

.float-animation {
  animation: float-animation var(--horror-float-duration) ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-flicker {
  animation: flicker-animation 0.5s infinite alternate;
}

.gradient-shadow {
  background: linear-gradient(-45deg, var(--horror-midnight), var(--horror-shadow), var(--horror-blood), var(--horror-red));
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

.horror-glow {
  transition: all 0.3s ease;
}

.horror-glow:hover {
  animation: ember-glow 1.5s infinite alternate;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--horror-midnight);
}

::-webkit-scrollbar-thumb {
  background: var(--horror-red);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--horror-flame);
}

/* Selection Styling */
::selection {
  background: var(--horror-red);
  color: white;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .monster-title {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }
}

/* Focus States for Accessibility */
button:focus,
a:focus {
  outline: 2px solid var(--horror-flame);
  outline-offset: 2px;
}

/* Loading Animation */
@keyframes spin-horror {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-horror {
  animation: spin-horror 1s linear infinite;
}

/* Background Textures */
.horror-texture {
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 0, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.1) 0%, transparent 50%);
}

/* Interactive Elements */
.horror-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.horror-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.horror-button:hover::before {
  left: 100%;
}

/* Card Hover Effects */
.horror-card {
  transition: all 0.3s ease;
  position: relative;
}

.horror-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 69, 0, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.horror-card:hover::after {
  opacity: 1;
}

/* Text Effects */
.horror-text-glow {
  text-shadow:
    0 0 5px var(--horror-red),
    0 0 10px var(--horror-red),
    0 0 15px var(--horror-red);
}

.horror-text-flicker {
  animation: flicker-animation 0.3s infinite alternate;
}

/* Utility Classes */
.bg-horror-midnight { background-color: var(--horror-midnight); }
.bg-horror-shadow { background-color: var(--horror-shadow); }
.bg-horror-red { background-color: var(--horror-red); }
.bg-horror-blood { background-color: var(--horror-blood); }
.bg-horror-flame { background-color: var(--horror-flame); }
.bg-horror-ember { background-color: var(--horror-ember); }

.text-horror-midnight { color: var(--horror-midnight); }
.text-horror-shadow { color: var(--horror-shadow); }
.text-horror-red { color: var(--horror-red); }
.text-horror-blood { color: var(--horror-blood); }
.text-horror-flame { color: var(--horror-flame); }
.text-horror-ember { color: var(--horror-ember); }

.border-horror-midnight { border-color: var(--horror-midnight); }
.border-horror-shadow { border-color: var(--horror-shadow); }
.border-horror-red { border-color: var(--horror-red); }
.border-horror-blood { border-color: var(--horror-blood); }
.border-horror-flame { border-color: var(--horror-flame); }
.border-horror-ember { border-color: var(--horror-ember); }

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  body {
    background-color: var(--horror-midnight);
    color: #ffffff;
  }
}