@import url('https://fonts.googleapis.com/css2?family=Creepster&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* MonsterJam Halloween Theme */
    --background: 0 0% 3%;
    --foreground: 0 0% 98%;

    --card: 0 0% 6%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 98%;

    /* Horror Red Primary */
    --primary: 0 85% 45%;
    --primary-foreground: 0 0% 98%;

    /* Blood Red Secondary */
    --secondary: 0 70% 25%;
    --secondary-foreground: 0 0% 95%;

    /* Dark Muted */
    --muted: 0 0% 12%;
    --muted-foreground: 0 0% 60%;

    /* Fire Orange Accent */
    --accent: 15 90% 55%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 10%;
    --ring: 0 85% 45%;

    --radius: 0.75rem;

    /* Horror Theme Colors */
    --horror-red: 0 85% 45%;
    --blood-red: 0 70% 25%;
    --flame-orange: 15 90% 55%;
    --ember-orange: 25 85% 45%;
    --shadow-black: 0 0% 3%;
    --midnight-black: 0 0% 6%;

    /* Gradients */
    --gradient-fire: linear-gradient(135deg, hsl(0 85% 45%), hsl(15 90% 55%), hsl(25 85% 45%));
    --gradient-shadow: linear-gradient(180deg, hsl(0 0% 3%), hsl(0 0% 1%));
    --gradient-ember: linear-gradient(45deg, hsl(0 70% 25%), hsl(15 80% 35%));

    /* Shadows */
    --shadow-horror: 0 10px 30px -5px hsl(0 85% 25% / 0.4);
    --shadow-flame: 0 0 40px hsl(15 90% 55% / 0.3);
    --shadow-deep: 0 25px 50px -12px hsl(0 0% 0% / 0.8);

    /* Animations */
    --transition-horror: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    --transition-flame: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Enhanced Dark Horror Theme */
    --background: 0 0% 2%;
    --foreground: 0 0% 98%;

    --card: 0 0% 4%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 3%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 90% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 75% 20%;
    --secondary-foreground: 0 0% 95%;

    --muted: 0 0% 8%;
    --muted-foreground: 0 0% 65%;

    --accent: 15 95% 60%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 90% 55%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 12%;
    --input: 0 0% 8%;
    --ring: 0 90% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

html {
  scroll-behavior: smooth;
}

body {
  @apply bg-background text-foreground;
}
}

/* Horror Theme Animations & Effects */
@layer components {
  .horror-glow {
    box-shadow: var(--shadow-horror);
  }

  .flame-glow {
    box-shadow: var(--shadow-flame);
  }

  .gradient-fire {
    background: var(--gradient-fire);
  }

  .gradient-shadow {
    background: var(--gradient-shadow);
  }

  .gradient-ember {
    background: var(--gradient-ember);
  }

  .monster-title {
    background: linear-gradient(135deg, hsl(0 90% 60%), hsl(15 85% 55%), hsl(25 80% 50%));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px hsl(0 85% 45% / 0.5);
  }

  .flicker-animation {
    animation: flicker 2s infinite alternate;
  }

  .pulse-horror {
    animation: pulse-horror 3s ease-in-out infinite;
  }

  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  .spooky-text {
    font-family: 'Creepster', cursive, fantasy;
    text-shadow:
      0 0 10px hsl(0 85% 45% / 0.8),
      0 0 20px hsl(0 85% 45% / 0.6),
      0 0 30px hsl(0 85% 45% / 0.4),
      2px 2px 4px hsl(0 0% 0% / 0.8);
    letter-spacing: 0.1em;
  }

  .dripping-effect {
    position: relative;
  }

  .dripping-effect::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(to bottom,
      hsl(0 85% 45% / 0.8) 0%,
      hsl(0 70% 35% / 0.6) 50%,
      transparent 100%);
    filter: blur(1px);
    border-radius: 0 0 50% 50%;
    animation: drip 3s ease-in-out infinite;
  }

  @keyframes drip {
    0%, 100% {
      transform: scaleY(1);
      opacity: 0.6;
    }
    50% {
      transform: scaleY(1.3);
      opacity: 0.9;
    }
  }

}

/* Horror Keyframes */
@layer utilities {
  @keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
    75% { opacity: 0.9; }
  }

  @keyframes pulse-horror {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 20px hsl(0 85% 45% / 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 40px hsl(0 85% 45% / 0.6);
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}